import {
  <PERSON><PERSON>,
  But<PERSON>,
  Icon<PERSON>utton,
  Input,
  Typography,
} from "@apollo/ui"
import { <PERSON>, Heart, Setting } from "@design-systems/apollo-icons"

const apolloComponents = [
  {
    title: "Button",
    href: "@apollo/ui/Components/Inputs/Button",
    description: "Primary action component with multiple variants and sizes",
    keywords: ["button", "action", "click", "primary", "secondary", "filled", "outline"],
    component: (
      <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
        <Button>Primary</Button>
        <Button color="negative">Negative</Button>
      </div>
    ),
  },
  {
    title: "Icon Button",
    href: "@apollo/ui/Components/Inputs/IconButton",
    description: "Compact button with icon for actions in limited space",
    keywords: ["icon", "button", "compact", "action", "small"],
    component: (
      <div style={{ display: "flex", gap: "8px" }}>
        <IconButton><Heart size={20} /></IconButton>
        <IconButton color="negative"><Setting size={20} /></IconButton>
      </div>
    ),
  },
  {
    title: "Input",
    href: "@apollo/ui/Components/Inputs/Input",
    description: "Text input field with validation and decorators",
    keywords: ["input", "text", "field", "form", "validation"],
    component: (
      <Input
        placeholder="Enter text..."
        startDecorator={<Search size={16} />}
        style={{ width: "200px" }}
      />
    ),
  },
  {
    title: "Typography",
    href: "@apollo/ui/Components/DataDisplay/Typography",
    description: "Text component with consistent styling and hierarchy",
    keywords: ["text", "typography", "heading", "body", "title"],
    component: (
      <div style={{ display: "flex", flexDirection: "column", gap: "4px" }}>
        <Typography level="headlineSmall">Headline</Typography>
        <Typography level="bodyLarge">Body text example</Typography>
        <Typography level="bodyMedium" style={{ color: "#6c757d" }}>Secondary text</Typography>
      </div>
    ),
  },
  {
    title: "Alert",
    href: "@apollo/ui/Components/Feedback/Alert",
    description: "Messaging component for user feedback and notifications",
    keywords: ["alert", "message", "feedback", "notification"],
    component: (
      <Alert type="information">This is an info alert</Alert>
    ),
  },
  {
    title: "Toast",
    href: "@apollo/ui/Components/Feedback/Toast",
    description: "Brief notification messages for user feedback",
    keywords: ["toast", "notification", "brief", "message"],
    component: (
      <Alert type="information">This is an info alert</Alert>
    ),
  },
  {
    title: "Modal",
    href: "@apollo/ui/Components/Feedback/Modal",
    description: "Dialog window for user interaction and information",
    keywords: ["modal", "dialog", "window", "interaction", "information"],
    component: (
      <img src={"/assets/apollo-ui/modal/action-do.png"} alt="modal without close button" style={{ maxWidth: "100%", padding: 24 }} />
    ),
  },
]

export default apolloComponents
