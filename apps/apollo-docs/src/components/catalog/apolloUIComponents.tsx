import {
  Accordion,
  Alert,
  Autocomplete,
  Badge,
  Breadcrumbs,
  Button,
  CapsuleTab,
  Checkbox,
  Chip,
  DateInput,
  FloatButton,
  IconButton,
  Input,
  Pagination,
  Radio,
  Select,
  SortingIcon,
  Switch,
  Tabs,
  Textarea,
  Toast,
  Typography,
  UploadBox,
} from "@apollo/ui"
import { Search, Heart, Setting, Plus, Calendar, Upload } from "@design-systems/apollo-icons"

const apolloUIComponents = [
  {
    title: "Button",
    href: "@apollo/ui/Components/Inputs/Button",
    description: "Primary action component with multiple variants and sizes",
    keywords: ["button", "action", "click", "primary", "secondary", "filled", "outline"],
    component: (
      <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
        <Button variant="filled" size="large">Primary</Button>
        <Button variant="outline" size="large">Secondary</Button>
      </div>
    ),
  },
  {
    title: "Icon Button",
    href: "@apollo/ui/Components/Inputs/IconButton",
    description: "Compact button with icon for actions in limited space",
    keywords: ["icon", "button", "compact", "action", "small"],
    component: (
      <div style={{ display: "flex", gap: "8px" }}>
        <IconButton><Heart size={20} /></IconButton>
        <IconButton><Setting size={20} /></IconButton>
      </div>
    ),
  },
  {
    title: "Float Button",
    href: "@apollo/ui/Components/Inputs/FloatButton",
    description: "Floating action button for primary actions",
    keywords: ["float", "floating", "action", "primary", "fab"],
    component: (
      <FloatButton icon={<Plus size={20} />} label="Add Item" />
    ),
  },
  {
    title: "Input",
    href: "@apollo/ui/Components/Inputs/Input",
    description: "Text input field with validation and decorators",
    keywords: ["input", "text", "field", "form", "validation"],
    component: (
      <Input 
        placeholder="Enter text..." 
        startDecorator={<Search size={16} />}
        style={{ width: "200px" }}
      />
    ),
  },
  {
    title: "Textarea",
    href: "@apollo/ui/Components/Inputs/Textarea",
    description: "Multi-line text input for longer content",
    keywords: ["textarea", "text", "multiline", "input", "form"],
    component: (
      <Textarea 
        placeholder="Enter description..." 
        rows={3}
        style={{ width: "200px" }}
      />
    ),
  },
  {
    title: "Checkbox",
    href: "@apollo/ui/Components/Inputs/Checkbox",
    description: "Selection control for multiple choices",
    keywords: ["checkbox", "selection", "multiple", "choice", "form"],
    component: (
      <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
        <Checkbox label="Option 1" defaultChecked />
        <Checkbox label="Option 2" />
      </div>
    ),
  },
  {
    title: "Radio",
    href: "@apollo/ui/Components/Inputs/Radio",
    description: "Selection control for single choice from multiple options",
    keywords: ["radio", "selection", "single", "choice", "form"],
    component: (
      <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
        <Radio name="example" label="Option A" defaultChecked />
        <Radio name="example" label="Option B" />
      </div>
    ),
  },
  {
    title: "Switch",
    href: "@apollo/ui/Components/Inputs/Switch",
    description: "Toggle control for binary states",
    keywords: ["switch", "toggle", "binary", "on", "off"],
    component: (
      <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
        <Switch defaultChecked />
        <Switch />
      </div>
    ),
  },
  {
    title: "Select",
    href: "@apollo/ui/Components/Inputs/Select",
    description: "Dropdown selection from a list of options",
    keywords: ["select", "dropdown", "options", "choice", "menu"],
    component: (
      <Select placeholder="Choose option..." style={{ width: "150px" }}>
        <option value="1">Option 1</option>
        <option value="2">Option 2</option>
        <option value="3">Option 3</option>
      </Select>
    ),
  },
  {
    title: "Autocomplete",
    href: "@apollo/ui/Components/Inputs/Autocomplete",
    description: "Input with searchable dropdown suggestions",
    keywords: ["autocomplete", "search", "suggestions", "dropdown", "filter"],
    component: (
      <Autocomplete 
        placeholder="Search..."
        style={{ width: "200px" }}
        options={[
          { label: "Apple", value: "apple" },
          { label: "Banana", value: "banana" },
          { label: "Cherry", value: "cherry" }
        ]}
      />
    ),
  },
  {
    title: "Date Input",
    href: "@apollo/ui/Components/Inputs/DateInput",
    description: "Date picker input with calendar interface",
    keywords: ["date", "picker", "calendar", "time", "input"],
    component: (
      <DateInput 
        placeholder="Select date..."
        startDecorator={<Calendar size={16} />}
        style={{ width: "180px" }}
      />
    ),
  },
  {
    title: "Upload Box",
    href: "@apollo/ui/Components/Inputs/UploadBox",
    description: "File upload area with drag and drop support",
    keywords: ["upload", "file", "drag", "drop", "attachment"],
    component: (
      <UploadBox style={{ width: "200px", height: "120px" }}>
        <Upload size={24} />
        <Typography level="bodySmall">Drop files here</Typography>
      </UploadBox>
    ),
  },
  {
    title: "Typography",
    href: "@apollo/ui/Components/Data Display/Typography",
    description: "Text component with semantic levels and styling",
    keywords: ["typography", "text", "heading", "body", "display"],
    component: (
      <div style={{ display: "flex", flexDirection: "column", gap: "4px" }}>
        <Typography level="headlineSmall">Headline</Typography>
        <Typography level="bodyLarge">Body text example</Typography>
        <Typography level="labelMedium" color="secondary">Label text</Typography>
      </div>
    ),
  },
  {
    title: "Badge",
    href: "@apollo/ui/Components/Data Display/Badge",
    description: "Small status indicator or count display",
    keywords: ["badge", "status", "count", "indicator", "notification"],
    component: (
      <div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
        <Badge count={5} />
        <Badge count={99} />
        <Badge dot />
      </div>
    ),
  },
  {
    title: "Chip",
    href: "@apollo/ui/Components/Data Display/Chip",
    description: "Compact element for tags, filters, or selections",
    keywords: ["chip", "tag", "filter", "selection", "removable"],
    component: (
      <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
        <Chip label="Tag 1" />
        <Chip label="Removable" onClose={() => {}} />
        <Chip label="Disabled" disabled />
      </div>
    ),
  },
  {
    title: "Breadcrumbs",
    href: "@apollo/ui/Components/Navigation/Breadcrumbs",
    description: "Navigation trail showing current page location",
    keywords: ["breadcrumbs", "navigation", "trail", "path", "hierarchy"],
    component: (
      <Breadcrumbs>
        <Typography level="bodyMedium">Home</Typography>
        <Typography level="bodyMedium">Category</Typography>
        <Typography level="bodyMedium" color="primary">Current</Typography>
      </Breadcrumbs>
    ),
  },
  {
    title: "Pagination",
    href: "@apollo/ui/Components/Navigation/Pagination",
    description: "Navigation control for paginated content",
    keywords: ["pagination", "navigation", "pages", "next", "previous"],
    component: (
      <Pagination
        currentPage={2}
        totalPages={5}
        onPageChange={() => {}}
      />
    ),
  },
  {
    title: "Tabs",
    href: "@apollo/ui/Components/Navigation/Tabs",
    description: "Tabbed interface for organizing content sections",
    keywords: ["tabs", "navigation", "sections", "content", "switch"],
    component: (
      <Tabs defaultValue="tab1" style={{ width: "200px" }}>
        <Tabs.List>
          <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
          <Tabs.Tab value="tab2">Tab 2</Tabs.Tab>
        </Tabs.List>
        <Tabs.Panel value="tab1">Content 1</Tabs.Panel>
        <Tabs.Panel value="tab2">Content 2</Tabs.Panel>
      </Tabs>
    ),
  },
  {
    title: "Capsule Tab",
    href: "@apollo/ui/Components/Navigation/CapsuleTab",
    description: "Pill-style tab navigation with rounded appearance",
    keywords: ["capsule", "tab", "pill", "navigation", "rounded"],
    component: (
      <CapsuleTab
        tabs={[
          { label: "All", value: "all" },
          { label: "Active", value: "active" },
          { label: "Completed", value: "completed" }
        ]}
        defaultValue="all"
      />
    ),
  },
  {
    title: "Alert",
    href: "@apollo/ui/Components/Feedback/Alert",
    description: "Important messages and notifications",
    keywords: ["alert", "message", "notification", "warning", "error", "success"],
    component: (
      <Alert severity="info" style={{ width: "250px" }}>
        This is an informational alert
      </Alert>
    ),
  },
  {
    title: "Toast",
    href: "@apollo/ui/Components/Feedback/Toast",
    description: "Temporary notification messages",
    keywords: ["toast", "notification", "temporary", "message", "popup"],
    component: (
      <Toast
        message="Operation completed successfully"
        type="success"
        visible={true}
      />
    ),
  },
  {
    title: "Modal",
    href: "@apollo/ui/Components/Overlay/Modal",
    description: "Dialog overlay for focused interactions",
    keywords: ["modal", "dialog", "overlay", "popup", "focus"],
    component: (
      <div style={{ padding: "16px", border: "1px solid #e0e0e0", borderRadius: "8px" }}>
        <Typography level="bodyMedium">Modal Preview</Typography>
        <Typography level="labelSmall" color="secondary">Click component for full demo</Typography>
      </div>
    ),
  },
  {
    title: "Accordion",
    href: "@apollo/ui/Components/Layout/Accordion",
    description: "Collapsible content sections",
    keywords: ["accordion", "collapsible", "expand", "collapse", "sections"],
    component: (
      <Accordion style={{ width: "200px" }}>
        <Accordion.Item value="item1">
          <Accordion.Header>Section 1</Accordion.Header>
          <Accordion.Panel>Content for section 1</Accordion.Panel>
        </Accordion.Item>
      </Accordion>
    ),
  },
  {
    title: "Sorting Icon",
    href: "@apollo/ui/Components/Utilities/SortingIcon",
    description: "Icon indicating sort direction in tables",
    keywords: ["sorting", "icon", "table", "direction", "asc", "desc"],
    component: (
      <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
        <SortingIcon status="asc" />
        <SortingIcon status="desc" />
        <SortingIcon status="none" />
      </div>
    ),
  },
  {
    title: "Portal",
    href: "@apollo/ui/Components/Utilities/Portal",
    description: "Render content outside component tree",
    keywords: ["portal", "render", "outside", "tree", "teleport"],
    component: (
      <div style={{ padding: "16px", border: "1px solid #e0e0e0", borderRadius: "8px" }}>
        <Typography level="bodyMedium">Portal Component</Typography>
        <Typography level="labelSmall" color="secondary">Renders content elsewhere</Typography>
      </div>
    ),
  },
]

export default apolloUIComponents
