import React from "react"
import { Typography } from "@apollo/ui"
import { Meta } from "@storybook/addon-docs/blocks"

import CautionCard from "../../components/caution-card/CautionCard"

<Meta title="@apollo∕ui/Quick Start" tags={["docs"]} />

<div
  style={{
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
    gap: 16,
    padding: "16px 0 32px",
  }}
>
  <Typography
    align="center"
    level="displayMedium"
    style={{ margin: "0px", color: "#121212" }}
  >Quick Start</Typography>
  <Typography
    align="center"
    level="titleLarge"
    style={{ color: "var(--sb-secondary-text-color)" }}
  >Get started with Apollo UI and build amazing user interfaces!</Typography>
</div>

## Installation

Install Apollo UI using your preferred package manager:

```bash
# with yarn
yarn add @apollo/ui

# with pnpm
pnpm add @apollo/ui

# with npm
npm install @apollo/ui
```

<CautionCard>
  <p>
    <strong>Peer Dependencies:</strong> Apollo UI requires React 18+ and TypeScript 4.9+ for optimal experience.
  </p>
</CautionCard>

## Setup

Apollo UI components are styled using CSS variables and design tokens. To ensure consistent styling, you need to use the `<ApolloProvider />` from the `@apollo/ui` package.

Place an `<ApolloProvider />` at the root of your app and pass theme as a prop.

```jsx title="App.js"
import React from 'react';
import { ApolloProvider, createThemeV2 } from "@apollo/ui"

const appTheme = createThemeV2()

function App({ children }) {
  return <ApolloProvider themeProps={{ theme: appTheme }}>{children}</ApolloProvider>
}
```

### Import Styles

Don't forget to import the CSS file in your main application file:

```jsx title="main.tsx or index.tsx"
import "@apollo/ui/style.css"
```

## Basic Usage

You can import and use components directly:

```jsx title="Button Example"
import React from 'react'
import { Button } from "@apollo/ui"

export default function Example() {
  return <Button>Hello World</Button>
}
```

```jsx title="Form Example"
import React, { useState } from 'react'
import { Input, Button, Typography } from "@apollo/ui"

export default function ContactForm() {
  const [email, setEmail] = useState('')
  
  return (
    <div style={{ maxWidth: 400, padding: 24 }}>
      <Typography level="headlineMedium" style={{ marginBottom: 16 }}>
        Contact Us
      </Typography>
      
      <Input
        label="Email"
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder="Enter your email"
        style={{ marginBottom: 16 }}
      />
      
      <Button variant="filled" size="large">
        Submit
      </Button>
    </div>
  )
}
```

## Theming

Apollo UI supports flexible theming through design tokens. You can customize the appearance by creating custom themes:

```jsx title="Custom Theme"
import { createThemeV2 } from "@apollo/ui"

const customTheme = createThemeV2({
  tokens: {
    colors: {
      primary: {
        primary: "#your-primary-color",
      }
    }
  }
})

function App() {
  return (
    <ApolloProvider themeProps={{ theme: customTheme }}>
      {/* Your app content */}
    </ApolloProvider>
  )
}
```

## TypeScript Support

Apollo UI is built with TypeScript and provides excellent type safety out of the box:

```tsx title="TypeScript Example"
import React from 'react'
import { Button, ButtonProps } from "@apollo/ui"

interface CustomButtonProps extends ButtonProps {
  loading?: boolean
}

const CustomButton: React.FC<CustomButtonProps> = ({ 
  loading, 
  children, 
  ...props 
}) => {
  return (
    <Button {...props} disabled={loading}>
      {loading ? 'Loading...' : children}
    </Button>
  )
}
```

## Component Categories

Apollo UI components are organized into several categories:

### Inputs
Form controls and interactive elements:
- `Button` - Primary action component
- `Input` - Text input field
- `Textarea` - Multi-line text input
- `Checkbox` - Multiple selection control
- `Radio` - Single selection control
- `Select` - Dropdown selection
- `Switch` - Toggle control

### Data Display
Components for presenting information:
- `Typography` - Text with semantic styling
- `Badge` - Status indicators and counts
- `Chip` - Compact tags and filters

### Navigation
Components for user navigation:
- `Breadcrumbs` - Navigation trail
- `Pagination` - Page navigation
- `Tabs` - Tabbed content sections

### Feedback
Components for user feedback:
- `Alert` - Important messages
- `Toast` - Temporary notifications

### Layout
Components for content structure:
- `Accordion` - Collapsible sections
- `Modal` - Dialog overlays

## Next Steps

Now that you have Apollo UI set up, explore the component catalog to see all available components with live examples and detailed documentation.

### Helpful Resources

- **Component Catalog**: Browse all components with interactive examples
- **Design Tokens**: Learn about the design token system
- **Theming Guide**: Deep dive into customization options
- **Accessibility**: Understanding built-in accessibility features

### Common Patterns

```jsx title="Layout Pattern"
import { Typography, Button, Input } from "@apollo/ui"

function Dashboard() {
  return (
    <div style={{ padding: 24 }}>
      <Typography level="displaySmall" style={{ marginBottom: 24 }}>
        Dashboard
      </Typography>
      
      <div style={{ display: 'flex', gap: 16, marginBottom: 24 }}>
        <Input placeholder="Search..." style={{ flex: 1 }} />
        <Button>Search</Button>
      </div>
      
      {/* Dashboard content */}
    </div>
  )
}
```

Ready to build something amazing? Start exploring the component catalog!
