import React from "react"
import { Typography } from "@apollo/ui"
import { Appstore, Code, Palette, Zap, Shield, Mobile } from "@design-systems/apollo-icons"
import ResourceCard from "../../components/resource-card/ResourceCard"
import { Meta } from "@storybook/addon-docs/blocks"

<Meta title="@apollo∕ui/Introduction" tags={["docs"]} />

<div
  style={{
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
    gap: 16,
    padding: "16px 0 32px",
  }}
>
  <Typography
    align="center"
    level="displayMedium"
    style={{ margin: "0px", color: "#121212" }}
  >@apollo/ui</Typography>
  <Typography
    align="center"
    level="titleLarge"
    style={{ color: "var(--sb-secondary-text-color)" }}
  >Modern React component library for building exceptional user interfaces</Typography>
  <div
    style={{
      display: "flex",
      gap: 8,
      flexWrap: "wrap",
      justifyContent: "center",
      padding: "32px",
      minWidth: "460px",
    }}
  >
    <Appstore size={64} style={{ color: "#27CA40" }} />
  </div>
</div>

## What is Apollo UI?

<Typography
  level="bodyLarge"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 32,
  }}
>
  Apollo UI is the main component library of the Apollo Design System, providing a comprehensive set of React components for building modern, accessible, and consistent user interfaces. Built with TypeScript and designed with flexibility in mind, it offers everything from basic inputs to complex data display components.
</Typography>

## Key Features

<div style={{ marginBottom: 32 }}>
  <ul style={{
    listStyle: "none",
    padding: 0,
    display: "grid",
    gap: 16,
    gridTemplateColumns: "repeat(auto-fit, minmax(280px, 1fr))"
  }}>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
    }}>
      <Code size={24} style={{ color: "#27CA40", flexShrink: 0, marginTop: 2 }} />
      <div>
        <strong>TypeScript First</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Built with TypeScript for excellent developer experience and type safety
        </span>
      </div>
    </li>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
    }}>
      <Shield size={24} style={{ color: "#27CA40", flexShrink: 0, marginTop: 2 }} />
      <div>
        <strong>Accessibility Built-in</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          WCAG compliant components with keyboard navigation and screen reader support
        </span>
      </div>
    </li>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
    }}>
      <Palette size={24} style={{ color: "#27CA40", flexShrink: 0, marginTop: 2 }} />
      <div>
        <strong>Design Token Integration</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Seamlessly integrates with Apollo design tokens for consistent theming
        </span>
      </div>
    </li>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
    }}>
      <Zap size={24} style={{ color: "#27CA40", flexShrink: 0, marginTop: 2 }} />
      <div>
        <strong>Performance Optimized</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Tree-shakeable components with minimal bundle impact
        </span>
      </div>
    </li>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
    }}>
      <Mobile size={24} style={{ color: "#27CA40", flexShrink: 0, marginTop: 2 }} />
      <div>
        <strong>Responsive Design</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Mobile-first approach with responsive behavior across all devices
        </span>
      </div>
    </li>
  </ul>
</div>

## Component Categories

Apollo UI organizes components into logical categories to help you find what you need:

<div style={{ marginBottom: 32 }}>
  <div style={{
    display: "grid",
    gap: 16,
    gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))"
  }}>
    <div style={{
      padding: 20,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      backgroundColor: "var(--sb-ui-background-color)",
    }}>
      <Typography level="titleMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
        Inputs
      </Typography>
      <Typography level="bodyMedium" style={{ color: "var(--sb-secondary-text-color)" }}>
        Button, Input, Textarea, Checkbox, Radio, Select, Switch, and more form controls
      </Typography>
    </div>
    <div style={{
      padding: 20,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      backgroundColor: "var(--sb-ui-background-color)",
    }}>
      <Typography level="titleMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
        Data Display
      </Typography>
      <Typography level="bodyMedium" style={{ color: "var(--sb-secondary-text-color)" }}>
        Typography, Badge, Chip, and components for presenting information
      </Typography>
    </div>
    <div style={{
      padding: 20,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      backgroundColor: "var(--sb-ui-background-color)",
    }}>
      <Typography level="titleMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
        Navigation
      </Typography>
      <Typography level="bodyMedium" style={{ color: "var(--sb-secondary-text-color)" }}>
        Breadcrumbs, Pagination, Tabs, and components for user navigation
      </Typography>
    </div>
    <div style={{
      padding: 20,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      backgroundColor: "var(--sb-ui-background-color)",
    }}>
      <Typography level="titleMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
        Feedback
      </Typography>
      <Typography level="bodyMedium" style={{ color: "var(--sb-secondary-text-color)" }}>
        Alert, Toast, and components for providing user feedback
      </Typography>
    </div>
    <div style={{
      padding: 20,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      backgroundColor: "var(--sb-ui-background-color)",
    }}>
      <Typography level="titleMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
        Layout
      </Typography>
      <Typography level="bodyMedium" style={{ color: "var(--sb-secondary-text-color)" }}>
        Accordion, Modal, and components for structuring content
      </Typography>
    </div>
    <div style={{
      padding: 20,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
      backgroundColor: "var(--sb-ui-background-color)",
    }}>
      <Typography level="titleMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
        Utilities
      </Typography>
      <Typography level="bodyMedium" style={{ color: "var(--sb-secondary-text-color)" }}>
        Portal, SortingIcon, and helper components for enhanced functionality
      </Typography>
    </div>
  </div>
</div>

## Getting Started

Ready to start building with Apollo UI? Check out our comprehensive guides:

<div
  style={{
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(260px, 1fr))",
    gap: 16,
    marginBottom: 32,
  }}
>
  <ResourceCard
    page="apollo∕ui-quick-start"
    title="Quick Start"
    description="Get up and running with Apollo UI in minutes"
    icon={<Zap size={24} />}
  />
  <ResourceCard
    page="apollo∕ui-catalog"
    title="Component Catalog"
    description="Browse all available components with live examples"
    icon={<Appstore size={24} />}
  />
</div>

## Design System Integration

Apollo UI is part of the larger Apollo Design System ecosystem, working seamlessly with:

- **Design Tokens**: Consistent colors, spacing, typography, and more
- **Icons**: Comprehensive icon library with consistent styling
- **Theming**: Flexible theming system supporting light/dark modes
- **Accessibility**: Built-in accessibility features across all components
